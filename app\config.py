import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()


class Settings:
    # Mongo
    # MongoDB connection string - must be set in environment variables
    MONGODB_URI: str = os.getenv("MONGODB_URI") or ""
    if not MONGODB_URI:
        raise ValueError("MONGODB_URI environment variable is required")

    DB_NAME: str = os.getenv("DB_NAME", "aba_client_portal")

    # TLS/SSL
    # Allow bypassing TLS certificate validation in development
    # DO NOT ENABLE IN PRODUCTION.
    TLS_ALLOW_INVALID_CERTS: bool = os.getenv("TLS_ALLOW_INVALID_CERTS", "0") == "1"

    # Server/resource limits
    # Max concurrent in-flight HTTP requests handled by this process
    MAX_CONCURRENCY: int = int(os.getenv("MAX_CONCURRENCY", "100"))
    # Max allowed request body size in bytes (default 10 MiB)
    MAX_BODY_SIZE_BYTES: int = int(os.getenv("MAX_BODY_SIZE_BYTES", str(10 * 1024 * 1024)))

    # Auth
    JWT_SECRET: str = os.getenv("JWT_SECRET", "change-me-in-prod")
    JWT_ALGORITHM: str = os.getenv("JWT_ALGORITHM", "HS256")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "60"))

    # Demo seed
    SEED_DEMO: bool = os.getenv("SEED_DEMO", "1") == "1"

    # Environment and error sanitization
    APP_ENV: str = os.getenv("APP_ENV", "development")  # development|staging|production
    SANITIZE_ERRORS: bool = os.getenv("SANITIZE_ERRORS", "0") == "1" or APP_ENV == "production"

    @property
    def access_token_expires(self) -> timedelta:
        return timedelta(minutes=self.ACCESS_TOKEN_EXPIRE_MINUTES)


settings = Settings()
