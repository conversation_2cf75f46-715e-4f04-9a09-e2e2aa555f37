from contextlib import asynccontextmanager
from fastapi import FastAP<PERSON>, status
from fastapi.responses import JSONResponse
from app.db import connect_to_mongo, close_mongo_connection, get_db
from app.routers import (
    auth as auth_router,
    clients as clients_router,
    sessions as sessions_router,
    analytics as analytics_router,
    admin as admin_router,
    users as users_router,
)
from app.config import settings
from app.middleware import ConcurrencyLimitMiddleware, BodySizeLimitMiddleware


@asynccontextmanager
async def lifespan(app: FastAPI):
    await connect_to_mongo()
    try:
        yield
    finally:
        await close_mongo_connection()


app = FastAPI(title="ABA Client Portal API", version="0.1.0", lifespan=lifespan)
# Add conservative safeguards for resource usage
app.add_middleware(ConcurrencyLimitMiddleware, max_concurrency=settings.MAX_CONCURRENCY)
app.add_middleware(BodySizeLimitMiddleware, max_body_size=settings.MAX_BODY_SIZE_BYTES)

# Map InvalidObjectId to HTTP 400
from app.utils import InvalidObjectId

from fastapi import Request
from fastapi.responses import JSONResponse as _JSONResponse

INVALID_ID_MSG = "Invalid id format" if settings.SANITIZE_ERRORS else "Invalid ObjectId"

@app.exception_handler(InvalidObjectId)
async def handle_invalid_object_id(_: Request, exc: InvalidObjectId):
    return _JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content={"detail": INVALID_ID_MSG})

# Also map PyMongo's InvalidId to HTTP 400
from bson.errors import InvalidId as PyMongoInvalidId

@app.exception_handler(PyMongoInvalidId)
async def handle_pymongo_invalid_id(_: Request, exc: PyMongoInvalidId):
    return _JSONResponse(status_code=status.HTTP_400_BAD_REQUEST, content={"detail": INVALID_ID_MSG})

from fastapi.exceptions import RequestValidationError
from fastapi import HTTPException as FastAPIHTTPException
from starlette.exceptions import HTTPException as StarletteHTTPException


def _detail_msg(msg: str) -> dict:
    return {"detail": msg}


def _sanitized_message_for_status(code: int) -> str:
    if code == 400:
        return "Bad request"
    if code == 401:
        return "Not authenticated"
    if code == 403:
        return "Forbidden"
    if code == 404:
        return "Not found"
    if code == 405:
        return "Method not allowed"
    if code == 409:
        return "Conflict"
    if code == 413:
        return "Request body too large"
    if code == 415:
        return "Unsupported media type"
    if code == 422:
        return "Validation error"
    if code in (500, 502, 503, 504):
        return "Internal server error"
    return "An error occurred"


@app.exception_handler(FastAPIHTTPException)
async def http_exception_handler(_: Request, exc: FastAPIHTTPException):
    if settings.SANITIZE_ERRORS:
        return _JSONResponse(
            status_code=exc.status_code,
            content=_detail_msg(_sanitized_message_for_status(exc.status_code)),
            headers=getattr(exc, "headers", None),
        )
    return _JSONResponse(
        status_code=exc.status_code,
        content={"detail": exc.detail},
        headers=getattr(exc, "headers", None),
    )


@app.exception_handler(StarletteHTTPException)
async def starlette_http_exception_handler(_: Request, exc: StarletteHTTPException):
    if settings.SANITIZE_ERRORS:
        return _JSONResponse(
            status_code=exc.status_code,
            content=_detail_msg(_sanitized_message_for_status(exc.status_code)),
        )
    return _JSONResponse(status_code=exc.status_code, content={"detail": exc.detail})


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(_: Request, exc: RequestValidationError):
    if settings.SANITIZE_ERRORS:
        return _JSONResponse(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            content=_detail_msg("Validation error"),
        )
    return _JSONResponse(
        status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
        content={"detail": exc.errors()},
    )


@app.exception_handler(Exception)
async def unhandled_exception_handler(_: Request, exc: Exception):
    if settings.SANITIZE_ERRORS:
        return _JSONResponse(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            content=_detail_msg("Internal server error"),
        )
    return _JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={"detail": str(exc)},
    )




# Overall health endpoint
@app.get("/health")
async def health():
    try:
        # DB check
        db = get_db()
        res = await db.command("ping")
        db_ok = res.get("ok") == 1
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "app": "running",
                "database": "disconnected",
                "error": "Internal server error" if settings.SANITIZE_ERRORS else str(e),
            },
        )

    if db_ok:
        return {"status": "healthy", "app": "running", "database": "connected"}

    return JSONResponse(
        status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
        content={
            "status": "unhealthy",
            "app": "running",
            "database": "disconnected",
            "error": f"Unexpected ping response: {res}",
        },
    )


# Health/root endpoint
@app.get("/")
async def root():
    return {"message": "ABA Client Portal API is running"}


@app.get("/hello/{name}")
async def say_hello(name: str):
    return {"message": f"Hello {name}"}

# Database health endpoint
@app.get("/health/db")
async def health_db():
    try:
        db = get_db()
        # Perform a ping command to verify connectivity
        res = await db.command("ping")
        if res.get("ok") == 1:
            return {"status": "healthy", "database": "connected"}
        # Unexpected response shape
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": f"Unexpected ping response: {res}",
            },
        )
    except Exception as e:
        return JSONResponse(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            content={
                "status": "unhealthy",
                "database": "disconnected",
                "error": str(e),
            },
        )



# Include feature routers
app.include_router(auth_router.router)
app.include_router(clients_router.router)
app.include_router(sessions_router.router)
app.include_router(analytics_router.router)
app.include_router(admin_router.router)
app.include_router(users_router.router)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("main:app", host="127.0.0.1", port=8000, reload=True)
