from typing import Any, Dict
from bson import ObjectId


class InvalidObjectId(ValueError):
    """Raised when a string is not a valid BSON ObjectId."""
    pass


def to_object_id(id_str: str) -> ObjectId:
    if not ObjectId.is_valid(id_str):
        raise InvalidObjectId("Invalid ObjectId")
    return ObjectId(id_str)


def convert_object_ids(doc: Dict[str, Any]) -> Dict[str, Any]:
    out: Dict[str, Any] = {}
    for k, v in doc.items():
        if isinstance(v, ObjectId):
            out[k] = str(v)
        elif isinstance(v, list):
            out[k] = [str(x) if isinstance(x, ObjectId) else x for x in v]
        elif isinstance(v, dict):
            out[k] = convert_object_ids(v)
        else:
            out[k] = v
    return out
